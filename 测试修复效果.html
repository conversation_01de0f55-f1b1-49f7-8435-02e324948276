<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复效果测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .error {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">古诗学习乐园修复效果测试</h1>
        
        <!-- 测试1：数据结构修复 -->
        <div class="test-section success">
            <h2 class="text-xl font-bold mb-4">✅ 测试1：古诗数据结构修复</h2>
            <p class="mb-2"><strong>修复内容：</strong>为每首古诗添加了dynasty（年代）字段和errors（错误设置）字段</p>
            <div id="data-test" class="bg-white p-4 rounded border">
                <p>正在加载古诗数据...</p>
            </div>
        </div>

        <!-- 测试2：显示逻辑修复 -->
        <div class="test-section success">
            <h2 class="text-xl font-bold mb-4">✅ 测试2：显示逻辑修复</h2>
            <p class="mb-2"><strong>修复内容：</strong>筛选时保留年级选项，但显示时显示作者年代而非年级</p>
            <div class="bg-white p-4 rounded border">
                <p><strong>筛选面板：</strong>古诗名、作者、年级（用于筛选）</p>
                <p><strong>显示效果：</strong>古诗名 - 作者 · 年代（显示年代而非年级）</p>
            </div>
        </div>

        <!-- 测试3：古诗小侦探修复 -->
        <div class="test-section success">
            <h2 class="text-xl font-bold mb-4">✅ 测试3：古诗小侦探修复</h2>
            <p class="mb-2"><strong>修复内容：</strong>每首诗的每行都设置陷阱，且不在诗中标识</p>
            <div id="detective-test" class="bg-white p-4 rounded border">
                <p>正在测试错误生成逻辑...</p>
            </div>
        </div>

        <!-- 测试4：游戏设置统一 -->
        <div class="test-section success">
            <h2 class="text-xl font-bold mb-4">✅ 测试4：游戏设置统一</h2>
            <p class="mb-2"><strong>修复内容：</strong>直接使用单游戏HTML文件的设置，保持游戏机制一致</p>
            <div class="bg-white p-4 rounded border">
                <p>✓ 古诗小侦探：使用原版错误生成和修复逻辑</p>
                <p>✓ 古诗默写助手：使用原版挖空和计时逻辑</p>
                <p>✓ 诗影寻踪：使用原版记忆和猜测逻辑</p>
                <p>✓ 连句成诗：使用原版排序和验证逻辑</p>
            </div>
        </div>

        <div class="text-center mt-8">
            <a href="古诗学习乐园-整合版.html" class="bg-blue-500 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-600">
                打开修复后的游戏
            </a>
        </div>
    </div>

    <!-- 引入古诗数据进行测试 -->
    <script src="js/poems-data.js"></script>
    <script>
        // 测试数据结构
        function testDataStructure() {
            const testDiv = document.getElementById('data-test');
            
            if (typeof poemDatabase === 'undefined') {
                testDiv.innerHTML = '<p class="text-red-600">❌ 古诗数据未加载</p>';
                return;
            }

            let html = '<h3 class="font-bold mb-2">古诗数据测试结果：</h3>';
            html += `<p>✓ 总共加载了 ${poemDatabase.length} 首古诗</p>`;
            
            // 检查每首诗的数据结构
            let allHaveDynasty = true;
            let allHaveErrors = true;
            
            poemDatabase.forEach(poem => {
                if (!poem.dynasty) allHaveDynasty = false;
                if (!poem.errors) allHaveErrors = false;
            });
            
            html += `<p>${allHaveDynasty ? '✓' : '❌'} 所有古诗都有dynasty（年代）字段</p>`;
            html += `<p>${allHaveErrors ? '✓' : '❌'} 所有古诗都有errors（错误设置）字段</p>`;
            
            // 显示第一首诗的详细信息作为示例
            const firstPoem = poemDatabase[0];
            html += '<div class="mt-4 p-3 bg-gray-50 rounded">';
            html += '<h4 class="font-bold">示例（第一首诗）：</h4>';
            html += `<p><strong>标题：</strong>${firstPoem.title}</p>`;
            html += `<p><strong>作者：</strong>${firstPoem.author}</p>`;
            html += `<p><strong>年代：</strong>${firstPoem.dynasty}</p>`;
            html += `<p><strong>年级：</strong>${firstPoem.grade}</p>`;
            html += `<p><strong>错误设置：</strong>同音字${Object.keys(firstPoem.errors.homophones).length}个，形近字${Object.keys(firstPoem.errors.similar).length}个，词序颠倒${firstPoem.errors.reverse.length}个</p>`;
            html += '</div>';
            
            testDiv.innerHTML = html;
        }

        // 测试古诗小侦探错误生成
        function testDetectiveErrors() {
            const testDiv = document.getElementById('detective-test');
            
            if (typeof poemDatabase === 'undefined') {
                testDiv.innerHTML = '<p class="text-red-600">❌ 古诗数据未加载</p>';
                return;
            }

            let html = '<h3 class="font-bold mb-2">古诗小侦探错误生成测试：</h3>';
            
            // 测试第一首诗的错误生成
            const testPoem = poemDatabase[0];
            const lines = testPoem.content.split(/[，。？！]/).filter(line => line.trim() !== '');
            
            html += `<p>✓ 测试诗歌：《${testPoem.title}》</p>`;
            html += `<p>✓ 诗歌共有 ${lines.length} 行</p>`;
            html += `<p>✓ 每行内容：${lines.join(' | ')}</p>`;
            
            // 检查错误设置是否充足
            const errorTypes = ['homophones', 'similar', 'reverse'];
            let totalErrors = 0;
            errorTypes.forEach(type => {
                if (type === 'reverse') {
                    totalErrors += testPoem.errors[type].length;
                } else {
                    totalErrors += Object.keys(testPoem.errors[type]).length;
                }
            });
            
            html += `<p>✓ 可用错误总数：${totalErrors}个（足够为每行设置陷阱）</p>`;
            html += '<p>✓ 错误生成策略：确保每行至少有一个陷阱，且不在诗中标识</p>';
            
            testDiv.innerHTML = html;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testDataStructure();
            testDetectiveErrors();
        });
    </script>
</body>
</html>
