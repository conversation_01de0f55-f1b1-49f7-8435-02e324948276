<html lang="zh-CN"><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>诗影寻踪 - 古诗记忆挑战游戏</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- Tailwind配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#4A2C2A', // 深棕色 - 沉稳、典雅
            secondary: '#E8C547', // 琥珀色 - 温暖、高贵
            accent: '#A37C40', // 赭石色 - 点缀色，增强层次感
            light: '#F8F4E3', // 象牙白 - 柔和背景
            dark: '#33261D', // 深褐色 - 文字颜色，增强可读性
            paper: '#F0EAD6', // 米黄色 - 纸张质感
          },
          fontFamily: {
            song: ['"Noto Serif SC"', 'serif'],
          },
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .poem-shadow {
        @apply border-2 border-dashed border-primary/30 bg-paper rounded-lg transition-all duration-300;
      }
      .poem-shadow:hover {
        @apply border-primary/70 bg-paper/80;
      }
      .poem-tile {
        @apply border-2 border-primary/50 bg-paper rounded-lg shadow-md p-3 transition-all duration-300;
      }
      .poem-tile:hover {
        @apply shadow-lg transform -translate-y-1;
      }
      .button-primary {
        @apply bg-primary text-white font-medium py-2 px-4 rounded-full shadow-md transition-all duration-300 hover:bg-primary/90 hover:shadow-lg active:scale-95;
      }
      .button-secondary {
        @apply bg-secondary text-dark font-medium py-2 px-4 rounded-full shadow-md transition-all duration-300 hover:bg-secondary/90 hover:shadow-lg active:scale-95;
      }
      .button-accent {
        @apply bg-accent text-white font-medium py-2 px-4 rounded-full shadow-md transition-all duration-300 hover:bg-accent/90 hover:shadow-lg active:scale-95;
      }
      .card {
        @apply bg-paper rounded-xl shadow-lg p-4 md:p-6 transition-all duration-300;
      }
      .card:hover {
        @apply shadow-xl;
      }
      .shake {
        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        transform: translate3d(0, 0, 0);
        backface-visibility: hidden;
        perspective: 1000px;
      }
      @keyframes shake {
        10%, 90% { transform: translate3d(-1px, 0, 0); }
        20%, 80% { transform: translate3d(2px, 0, 0); }
        30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
        40%, 60% { transform: translate3d(4px, 0, 0); }
      }
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .fade-out {
        animation: fadeOut 0.5s ease-in-out;
      }
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
      }
      .scale-in {
        animation: scaleIn 0.3s ease-out;
      }
      @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
      .title-glow {
        text-shadow: 0 0 10px rgba(232, 197, 71, 0.5);
      }
      .scroll-container {
        max-height: 400px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #4A2C2A #F8F4E3;
      }
      .scroll-container::-webkit-scrollbar {
        width: 6px;
      }
      .scroll-container::-webkit-scrollbar-track {
        background: #F8F4E3;
      }
      .scroll-container::-webkit-scrollbar-thumb {
        background-color: #4A2C2A;
        border-radius: 3px;
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px -5px rgba(74, 44, 42, 0.1), 0 10px 10px -5px rgba(74, 44, 42, 0.04);
      }
      .text-balance {
        text-wrap: balance;
      }
    }
  </style>
</head>
<body class="bg-light min-h-screen font-song text-dark">
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- 游戏标题 -->
    <div class="text-center mb-8">
      <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-bold text-primary title-glow">诗影寻踪</h1>
      <p class="text-[clamp(1.2rem,2vw,1.5rem)] text-dark/70 mt-2">古诗记忆挑战游戏</p>
    </div>
    
    <!-- 顶部控制区 -->
    <div class="mb-6">
      <!-- 可折叠的选择古诗区域 -->
      <details class="mb-4 card card-hover">
        <summary class="flex items-center justify-between cursor-pointer font-medium text-xl text-primary">
          <span>选择古诗</span>
          <i class="fa fa-chevron-down transition-transform duration-300"></i>
        </summary>
        <div class="mt-4 pt-4 border-t border-primary/20 scroll-container">
          <div class="grid grid-cols-1 gap-3">
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem1" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《村居》（清·高鼎）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem2" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《咏柳》（唐·贺知章）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem3" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《赋得古原草送别》（唐·白居易）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem4" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《晓出净慈寺送林子方》（宋·杨万里）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem5" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《绝句》（唐·杜甫）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem6" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《悯农（其一）》（唐·李绅）</span>
            </label>
            <label class="flex items-center p-4 rounded-lg hover:bg-primary/5 cursor-pointer transition-colors duration-200">
              <input type="checkbox" id="poem7" class="form-checkbox h-6 w-6 text-primary rounded" checked="">
              <span class="ml-3 text-[clamp(1.2rem,2vw,1.5rem)] text-balance">《舟夜书所见》（清·查慎行）</span>
            </label>
          </div>
          <div class="mt-6 flex justify-center">
            <button id="random-select" class="button-secondary mr-3 px-6 py-3 text-lg">
              <i class="fa fa-random mr-2"></i> 随机选择
            </button>
            <button id="select-all" class="button-secondary px-6 py-3 text-lg">
              <i class="fa fa-check-square-o mr-2"></i> 全选/取消
            </button>
          </div>
        </div>
      </details>
      
      <!-- 游戏信息区 -->
      <div class="flex flex-col md:flex-row justify-between items-center card card-hover">
        <div class="flex items-center mb-4 md:mb-0">
          <i class="fa fa-clock-o text-primary text-2xl mr-3"></i>
          <span id="timer" class="text-2xl font-medium">00:00</span>
        </div>
        <button id="start-btn" class="button-primary flex items-center px-8 py-3 text-lg">
          <i class="fa fa-play mr-2"></i> 开始挑战
        </button>
      </div>
    </div>
    
    <!-- 游戏主区域 -->
    <div id="game-container" class="card card-hover mb-6">
      <div id="poem-title" class="text-center text-2xl md:text-3xl font-bold text-primary mb-6">
        请选择一首古诗开始挑战
      </div>
      
      <div id="poem-display" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <!-- 诗句将在这里动态生成 -->
      </div>
      
      <div id="clue-container" class="hidden">
        <div id="clue-tile" class="poem-tile text-center mx-auto max-w-md cursor-pointer scale-in text-[clamp(1.3rem,2vw,1.6rem)] py-4"></div>
      </div>
    </div>
    
    <!-- 胜利弹窗 -->
    <div id="victory-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
      <div class="bg-paper rounded-xl shadow-2xl p-8 max-w-md w-full mx-4 scale-in">
        <div class="text-center">
          <div class="text-5xl text-secondary mb-5">
            <i class="fa fa-trophy"></i>
          </div>
          <h2 class="text-3xl font-bold text-primary mb-3">挑战成功！</h2>
          <p class="text-xl mb-6">您已成功完成了这首古诗的记忆挑战</p>
          <div class="bg-primary/10 rounded-lg p-5 mb-6">
            <p class="text-2xl font-bold text-primary">用时：<span id="final-time">00:00</span></p>
          </div>
          <button id="play-again" class="button-accent px-8 py-3 text-lg">
            <i class="fa fa-refresh mr-2"></i> 再来一首
          </button>
        </div>
      </div>
    </div>
    
    <script>
      // 内置诗歌数据
      const poems = [
        {
          title: "村居",
          author: "清·高鼎",
          lines: [
            "草长莺飞二月天",
            "拂堤杨柳醉春烟",
            "儿童散学归来早",
            "忙趁东风放纸鸢"
          ]
        },
        {
          title: "咏柳",
          author: "唐·贺知章",
          lines: [
            "碧玉妆成一树高",
            "万条垂下绿丝绦",
            "不知细叶谁裁出",
            "二月春风似剪刀"
          ]
        },
        {
          title: "赋得古原草送别",
          author: "唐·白居易",
          lines: [
            "离离原上草",
            "一岁一枯荣",
            "野火烧不尽",
            "春风吹又生"
          ]
        },
        {
          title: "晓出净慈寺送林子方",
          author: "宋·杨万里",
          lines: [
            "毕竟西湖六月中",
            "风光不与四时同",
            "接天莲叶无穷碧",
            "映日荷花别样红"
          ]
        },
        {
          title: "绝句",
          author: "唐·杜甫",
          lines: [
            "两个黄鹂鸣翠柳",
            "一行白鹭上青天",
            "窗含西岭千秋雪",
            "门泊东吴万里船"
          ]
        },
        {
          title: "悯农（其一）",
          author: "唐·李绅",
          lines: [
            "春种一粒粟",
            "秋收万颗子",
            "四海无闲田",
            "农夫犹饿死"
          ]
        },
        {
          title: "舟夜书所见",
          author: "清·查慎行",
          lines: [
            "月黑见渔灯",
            "孤光一点萤",
            "微微风簇浪",
            "散作满河星"
          ]
        }
      ];
      
      // 游戏状态
      let gameState = {
        currentPoem: null,
        poemLines: [],
        shuffledLines: [],
        placedLines: [],
        timer: null,
        startTime: 0,
        elapsedTime: 0,
        isPlaying: false,
        isMemorizing: false
      };
      
      // DOM元素
      const startBtn = document.getElementById('start-btn');
      const timerEl = document.getElementById('timer');
      const poemTitleEl = document.getElementById('poem-title');
      const poemDisplayEl = document.getElementById('poem-display');
      const clueContainerEl = document.getElementById('clue-container');
      const clueTileEl = document.getElementById('clue-tile');
      const victoryModalEl = document.getElementById('victory-modal');
      const finalTimeEl = document.getElementById('final-time');
      const playAgainBtn = document.getElementById('play-again');
      const randomSelectBtn = document.getElementById('random-select');
      const selectAllBtn = document.getElementById('select-all');
      
      // 音效生成函数
      const soundEffects = {
        success: () => {
          const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioCtx.createOscillator();
          const gainNode = audioCtx.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioCtx.destination);
          
          oscillator.type = 'sine';
          oscillator.frequency.setValueAtTime(880, audioCtx.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(440, audioCtx.currentTime + 0.3);
          
          gainNode.gain.setValueAtTime(0.5, audioCtx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioCtx.currentTime + 0.5);
          
          oscillator.start();
          oscillator.stop(audioCtx.currentTime + 0.5);
        },
        error: () => {
          const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioCtx.createOscillator();
          const gainNode = audioCtx.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioCtx.destination);
          
          oscillator.type = 'sawtooth';
          oscillator.frequency.setValueAtTime(220, audioCtx.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(110, audioCtx.currentTime + 0.3);
          
          gainNode.gain.setValueAtTime(0.5, audioCtx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioCtx.currentTime + 0.5);
          
          oscillator.start();
          oscillator.stop(audioCtx.currentTime + 0.5);
        }
      };
      
      // 格式化时间
      function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
      
      // 更新计时器
      function updateTimer() {
        const now = Date.now();
        gameState.elapsedTime = Math.floor((now - gameState.startTime) / 1000);
        timerEl.textContent = formatTime(gameState.elapsedTime);
      }
      
      // 开始计时
      function startTimer() {
        gameState.startTime = Date.now();
        gameState.timer = setInterval(updateTimer, 1000);
      }
      
      // 停止计时
      function stopTimer() {
        clearInterval(gameState.timer);
      }
      
      // 随机选择一首诗
      function selectRandomPoem() {
        const selectedPoems = [];
        for (let i = 0; i < poems.length; i++) {
          const checkbox = document.getElementById(`poem${i+1}`);
          if (checkbox.checked) {
            selectedPoems.push(poems[i]);
          }
        }
        
        if (selectedPoems.length === 0) {
          alert('请至少选择一首古诗！');
          return null;
        }
        
        const randomIndex = Math.floor(Math.random() * selectedPoems.length);
        return selectedPoems[randomIndex];
      }
      
      // 随机打乱数组
      function shuffleArray(array) {
        const newArray = [...array];
        for (let i = newArray.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
        }
        return newArray;
      }
      
      // 生成诗影占位符
      function createPoemShadows() {
        poemDisplayEl.innerHTML = '';
        gameState.poemLines.forEach((line, index) => {
          const shadowEl = document.createElement('div');
          shadowEl.className = 'poem-shadow aspect-[4/1] flex items-center justify-center text-primary/50';
          shadowEl.dataset.index = index;
          shadowEl.textContent = ''; // 不显示占位符文本
          shadowEl.addEventListener('click', handleShadowClick);
          poemDisplayEl.appendChild(shadowEl);
        });
      }
      
      // 显示完整诗句
      function displayFullPoem() {
        poemDisplayEl.innerHTML = '';
        gameState.poemLines.forEach((line, index) => {
          const lineEl = document.createElement('div');
          lineEl.className = 'poem-tile aspect-[4/1] flex items-center justify-center text-xl md:text-2xl';
          lineEl.textContent = line;
          poemDisplayEl.appendChild(lineEl);
        });
      }
      
      // 显示下一个线索
      function showNextClue() {
        if (gameState.shuffledLines.length === 0) {
          // 游戏胜利
          gameOver(true);
          return;
        }
        
        const clueIndex = Math.floor(Math.random() * gameState.shuffledLines.length);
        const clueLine = gameState.shuffledLines[clueIndex];
        
        clueTileEl.textContent = clueLine;
        clueTileEl.dataset.line = clueLine;
        clueTileEl.dataset.index = clueIndex;
        
        // 从shuffledLines中移除当前线索
        gameState.shuffledLines.splice(clueIndex, 1);
        
        clueContainerEl.classList.remove('hidden');
      }
      
      // 处理诗影点击事件
      function handleShadowClick(e) {
        if (!gameState.isPlaying || gameState.isMemorizing) return;
        
        const shadowEl = e.currentTarget;
        const shadowIndex = parseInt(shadowEl.dataset.index);
        const clueLine = clueTileEl.dataset.line;
        
        if (gameState.poemLines[shadowIndex] === clueLine) {
          // 放置正确
          soundEffects.success();
          
          // 更新诗影为正确的诗句
          shadowEl.className = 'poem-tile aspect-[4/1] flex items-center justify-center text-xl md:text-2xl scale-in';
          shadowEl.textContent = clueLine;
          shadowEl.removeEventListener('click', handleShadowClick);
          
          // 记录已放置的诗句
          gameState.placedLines.push({ index: shadowIndex, line: clueLine });
          
          // 显示下一个线索
          clueContainerEl.classList.add('fade-out');
          setTimeout(() => {
            showNextClue();
            clueContainerEl.classList.remove('fade-out');
          }, 300);
        } else {
          // 放置错误
          soundEffects.error();
          shadowEl.classList.add('shake');
          setTimeout(() => {
            shadowEl.classList.remove('shake');
          }, 500);
        }
      }
      
      // 游戏结束
      function gameOver(isVictory) {
        stopTimer();
        gameState.isPlaying = false;
        
        if (isVictory) {
          // 显示胜利弹窗
          finalTimeEl.textContent = formatTime(gameState.elapsedTime);
          victoryModalEl.classList.remove('hidden');
        }
      }
      
      // 开始游戏
      function startGame() {
        // 重置游戏状态
        gameState = {
          currentPoem: null,
          poemLines: [],
          shuffledLines: [],
          placedLines: [],
          timer: null,
          startTime: 0,
          elapsedTime: 0,
          isPlaying: false,
          isMemorizing: false
        };
        
        // 选择一首诗
        const poem = selectRandomPoem();
        if (!poem) return;
        
        gameState.currentPoem = poem;
        gameState.poemLines = [...poem.lines];
        gameState.shuffledLines = shuffleArray([...poem.lines]);
        gameState.isPlaying = true;
        gameState.isMemorizing = true;
        
        // 更新标题
        poemTitleEl.textContent = `${poem.title} - ${poem.author}`;
        
        // 显示完整诗句
        displayFullPoem();
        
        // 隐藏线索区
        clueContainerEl.classList.add('hidden');
        
        // 禁用开始按钮
        startBtn.disabled = true;
        startBtn.classList.add('opacity-50', 'cursor-not-allowed');
        startBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i> 记忆中...';
        
        // 5秒后开始游戏
        setTimeout(() => {
          gameState.isMemorizing = false;
          
          // 生成诗影占位符
          createPoemShadows();
          
          // 开始计时
          startTimer();
          
          // 显示第一个线索
          showNextClue();
          
          // 恢复开始按钮
          startBtn.disabled = false;
          startBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          startBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i> 重新开始';
        }, 5000);
      }
      
      // 随机选择按钮点击事件
      randomSelectBtn.addEventListener('click', () => {
        // 取消所有选择
        for (let i = 0; i < poems.length; i++) {
          document.getElementById(`poem${i+1}`).checked = false;
        }
        
        // 随机选择1-3首诗
        const numToSelect = Math.floor(Math.random() * 3) + 1;
        const selectedIndices = [];
        
        while (selectedIndices.length < numToSelect) {
          const randomIndex = Math.floor(Math.random() * poems.length);
          if (!selectedIndices.includes(randomIndex)) {
            selectedIndices.push(randomIndex);
            document.getElementById(`poem${randomIndex+1}`).checked = true;
          }
        }
      });
      
      // 全选/取消按钮点击事件
      selectAllBtn.addEventListener('click', () => {
        const allChecked = Array.from(document.querySelectorAll('input[type="checkbox"]')).every(checkbox => checkbox.checked);
        
        for (let i = 0; i < poems.length; i++) {
          document.getElementById(`poem${i+1}`).checked = !allChecked;
        }
      });
      
      // 开始按钮点击事件
      startBtn.addEventListener('click', startGame);
      
      // 再来一首按钮点击事件
      playAgainBtn.addEventListener('click', () => {
        victoryModalEl.classList.add('hidden');
        startGame();
      });
      
      // 折叠菜单切换动画
      document.querySelector('details summary').addEventListener('click', function() {
        const icon = this.querySelector('i');
        if (this.parentNode.open) {
          icon.style.transform = 'rotate(180deg)';
        } else {
          icon.style.transform = 'rotate(0)';
        }
      });
      
      // 初始化计时器显示
      timerEl.textContent = '00:00';
    </script>


</body></html>