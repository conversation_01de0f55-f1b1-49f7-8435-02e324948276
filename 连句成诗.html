<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连句成诗 - 小学生古诗学习游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    
    <!-- 配置Tailwind自定义颜色和字体 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#7A3E12',     // 更深的棕色作为主文本色
                        secondary: '#E8C89F',   // 更亮的棕褐色作为背景色
                        accent: '#A0522D',      // 赭色
                        background: '#FFF8E7',  // 更亮的米色背景
                        correct: '#22c55e',     // 正确选项颜色
                        wrong: '#ef4444',       // 错误选项颜色
                        option: '#6D3A0E',      // 选项按钮文字颜色（更深的棕色）
                        optionHover: '#4A2609', // 选项按钮悬停时的文字颜色
                    },
                    fontFamily: {
                        kai: ['"KaiTi"', '"STKaiti"', '"AR PL UKai CN"', 'serif'],
                    },
                }
            }
        }
    </script>
    
    <style type="text/tailwindcss">
        @layer utilities {
            .text-shadow {
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            }
            .poem-line {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.5s ease, transform 0.5s ease;
            }
            .poem-line.show {
                opacity: 1;
                transform: translateY(0);
            }
            .option-btn {
                transition: all 0.3s ease;
            }
            .option-btn:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            }
            .option-btn.correct {
                background-color: theme('colors.correct');
                transform: scale(1.05);
            }
            .option-btn.wrong {
                background-color: theme('colors.wrong');
                animation: shake 0.5s;
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            .timer-animation {
                transition: width 1s linear;
            }
            .modal-appear {
                animation: fadeIn 0.5s ease;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: scale(0.95); }
                to { opacity: 1; transform: scale(1); }
            }
            .dropdown-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }
            .dropdown-content.open {
                max-height: 500px; /* 设置一个足够大的值以容纳所有内容 */
                transition: max-height 0.5s ease-in;
            }
            .dropdown-arrow {
                transition: transform 0.3s ease;
            }
            .dropdown-arrow.open {
                transform: rotate(180deg);
            }
        }
    </style>
</head>
<body class="bg-background min-h-screen font-kai text-primary">
    <!-- 游戏容器 -->
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- 游戏标题 -->
        <header class="text-center mb-8">
            <h1 class="text-[clamp(2.2rem,6vw,3.8rem)] font-bold text-primary text-shadow">
                <i class="fa fa-book mr-2"></i>连句成诗
            </h1>
            <p class="text-xl text-accent mt-2">选择正确的下一句，完成古诗的学习</p>
        </header>
        
        <!-- 诗歌选择区域 - 折叠下拉框 -->
        <div class="bg-secondary/30 rounded-xl p-4 mb-6 shadow-md">
            <div class="flex items-center justify-between cursor-pointer" id="poem-selection-toggle">
                <h2 class="text-2xl font-semibold flex items-center">
                    <i class="fa fa-list-ul mr-2"></i>选择要学习的古诗
                </h2>
                <i class="fa fa-chevron-down dropdown-arrow text-xl transition-transform duration-300"></i>
            </div>
            <div class="dropdown-content mt-3 space-y-2" id="poem-selection-content">
                <!-- 诗歌选项将由JS动态生成 -->
            </div>
        </div>
        
        <!-- 顶部控制区 -->
        <div class="flex flex-col md:flex-row justify-between items-center bg-secondary/20 rounded-xl p-4 mb-6 shadow-md">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="flex items-center">
                    <i class="fa fa-clock-o text-2xl mr-2"></i>
                    <span class="text-xl font-semibold" id="timer">00:00</span>
                </div>
            </div>
            <button id="change-poem" class="bg-accent hover:bg-accent/80 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl flex items-center text-lg">
                <i class="fa fa-refresh mr-2"></i>换一首
            </button>
        </div>
        
        <!-- 游戏主区域 -->
        <div class="bg-white rounded-xl p-6 shadow-lg mb-6">
            <!-- 诗句展示区 -->
            <div class="mb-8 bg-background/70 rounded-lg p-6 min-h-[180px] flex flex-col items-center justify-center" id="poem-display">
                <div id="poem-title" class="text-2xl font-bold text-accent mb-5">《村居》 - 清·高鼎</div>
                <div id="poem-lines" class="space-y-4 w-full text-center"></div>
            </div>
            
            <!-- 选项区 -->
            <div id="options-container" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- 选项按钮将由JS动态生成 -->
            </div>
        </div>
        
        <!-- 游戏信息区 -->
        <div class="bg-secondary/20 rounded-xl p-4 text-center">
            <p class="text-base text-primary/80">选择正确的下一句，完成整首诗的学习。每首诗都有其独特的韵律和意境，在游戏中感受古诗的魅力！</p>
        </div>
    </div>
    
    <!-- 胜利模态框 -->
    <div id="victory-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
        <div class="bg-background rounded-xl p-8 max-w-md w-full mx-4 shadow-2xl modal-appear">
            <div class="text-center">
                <div class="text-5xl text-accent mb-4">
                    <i class="fa fa-trophy"></i>
                </div>
                <h2 class="text-2xl font-bold mb-2">太棒了！</h2>
                <p class="text-xl mb-4">你已经成功拿下这首诗！</p>
                <div class="text-2xl font-semibold mb-6">
                    <span id="completion-time">用时: 00:00</span>
                </div>
                <button id="play-again" class="bg-accent hover:bg-accent/80 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl text-lg">
                    <i class="fa fa-refresh mr-2"></i>再来一首
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // 内置诗歌数据
        const poems = [
            {
                title: "村居",
                author: "清·高鼎",
                lines: [
                    "草长莺飞二月天",
                    "拂堤杨柳醉春烟",
                    "儿童散学归来早",
                    "忙趁东风放纸鸢"
                ]
            },
            {
                title: "咏柳",
                author: "唐·贺知章",
                lines: [
                    "碧玉妆成一树高",
                    "万条垂下绿丝绦",
                    "不知细叶谁裁出",
                    "二月春风似剪刀"
                ]
            },
            {
                title: "赋得古原草送别",
                author: "唐·白居易",
                lines: [
                    "离离原上草",
                    "一岁一枯荣",
                    "野火烧不尽",
                    "春风吹又生"
                ]
            },
            {
                title: "晓出净慈寺送林子方",
                author: "宋·杨万里",
                lines: [
                    "毕竟西湖六月中",
                    "风光不与四时同",
                    "接天莲叶无穷碧",
                    "映日荷花别样红"
                ]
            },
            {
                title: "绝句",
                author: "唐·杜甫",
                lines: [
                    "两个黄鹂鸣翠柳",
                    "一行白鹭上青天",
                    "窗含西岭千秋雪",
                    "门泊东吴万里船"
                ]
            },
            {
                title: "悯农（其一）",
                author: "唐·李绅",
                lines: [
                    "春种一粒粟",
                    "秋收万颗子",
                    "四海无闲田",
                    "农夫犹饿死"
                ]
            },
            {
                title: "舟夜书所见",
                author: "清·查慎行",
                lines: [
                    "月黑见渔灯",
                    "孤光一点萤",
                    "微微风簇浪",
                    "散作满河星"
                ]
            }
        ];
        
        // 游戏状态
        let gameState = {
            selectedPoems: [...Array(poems.length).keys()], // 默认选择所有诗歌
            currentPoemIndex: null,
            currentLineIndex: 0,
            startTime: null,
            timerInterval: null,
            elapsedSeconds: 0
        };
        
        // DOM 元素
        const poemSelectionToggle = document.getElementById('poem-selection-toggle');
        const poemSelectionContent = document.getElementById('poem-selection-content');
        const dropdownArrow = document.querySelector('.dropdown-arrow');
        const poemTitle = document.getElementById('poem-title');
        const poemLines = document.getElementById('poem-lines');
        const optionsContainer = document.getElementById('options-container');
        const timerDisplay = document.getElementById('timer');
        const changePoemBtn = document.getElementById('change-poem');
        const victoryModal = document.getElementById('victory-modal');
        const completionTime = document.getElementById('completion-time');
        const playAgainBtn = document.getElementById('play-again');
        
        // 初始化游戏
        function initGame() {
            renderPoemSelection();
            startNewGame();
            
            // 事件监听器
            poemSelectionToggle.addEventListener('click', togglePoemSelection);
            changePoemBtn.addEventListener('click', startNewGame);
            playAgainBtn.addEventListener('click', () => {
                victoryModal.classList.add('hidden');
                startNewGame();
            });
        }
        
        // 切换诗歌选择下拉框
        function togglePoemSelection() {
            poemSelectionContent.classList.toggle('open');
            dropdownArrow.classList.toggle('open');
        }
        
        // 渲染诗歌选择区域
        function renderPoemSelection() {
            poemSelectionContent.innerHTML = '';
            
            poems.forEach((poem, index) => {
                const poemItem = document.createElement('div');
                poemItem.className = 'flex items-center p-2 rounded-lg hover:bg-secondary/20 transition-colors duration-200';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `poem-${index}`;
                checkbox.className = 'w-5 h-5 text-accent focus:ring-accent border-gray-300 rounded';
                checkbox.checked = gameState.selectedPoems.includes(index);
                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        if (!gameState.selectedPoems.includes(index)) {
                            gameState.selectedPoems.push(index);
                        }
                    } else {
                        gameState.selectedPoems = gameState.selectedPoems.filter(id => id !== index);
                    }
                    
                    // 如果没有选择任何诗歌，默认选择全部
                    if (gameState.selectedPoems.length === 0) {
                        gameState.selectedPoems = [...Array(poems.length).keys()];
                        document.querySelectorAll('#poem-selection-content input[type="checkbox"]').forEach(cb => {
                            cb.checked = true;
                        });
                    }
                    
                    // 重新开始游戏
                    startNewGame();
                });
                
                const label = document.createElement('label');
                label.htmlFor = `poem-${index}`;
                label.className = 'ml-2 text-base font-medium text-primary flex-1';
                label.textContent = `${poem.title} (${poem.author})`;
                
                poemItem.appendChild(checkbox);
                poemItem.appendChild(label);
                poemSelectionContent.appendChild(poemItem);
            });
        }
        
        // 开始新游戏
        function startNewGame() {
            // 停止当前计时器
            if (gameState.timerInterval) {
                clearInterval(gameState.timerInterval);
            }
            
            // 重置游戏状态
            gameState.elapsedSeconds = 0;
            timerDisplay.textContent = '00:00';
            
            // 随机选择一首诗歌
            if (gameState.selectedPoems.length > 0) {
                const randomIndex = Math.floor(Math.random() * gameState.selectedPoems.length);
                gameState.currentPoemIndex = gameState.selectedPoems[randomIndex];
                gameState.currentLineIndex = 0;
            } else {
                // 如果没有选择诗歌，默认选择第一首
                gameState.currentPoemIndex = 0;
                gameState.currentLineIndex = 0;
                gameState.selectedPoems = [0];
            }
            
            // 显示诗歌标题
            const currentPoem = poems[gameState.currentPoemIndex];
            poemTitle.textContent = `${currentPoem.title} - ${currentPoem.author}`;
            
            // 清空诗句展示区
            poemLines.innerHTML = '';
            
            // 显示第一句
            addLineToDisplay(currentPoem.lines[0]);
            
            // 生成选项
            generateOptions();
            
            // 开始计时
            startTimer();
        }
        
        // 添加诗句到展示区
        function addLineToDisplay(line) {
            const lineElement = document.createElement('div');
            lineElement.className = 'poem-line text-2xl font-medium text-primary';
            lineElement.textContent = line;
            poemLines.appendChild(lineElement);
            
            // 添加动画效果
            setTimeout(() => {
                lineElement.classList.add('show');
            }, 50);
        }
        
        // 生成选项
        function generateOptions() {
            optionsContainer.innerHTML = '';
            
            const currentPoem = poems[gameState.currentPoemIndex];
            const nextLineIndex = gameState.currentLineIndex + 1;
            
            // 如果已经是最后一句，游戏结束
            if (nextLineIndex >= currentPoem.lines.length) {
                endGame();
                return;
            }
            
            const correctLine = currentPoem.lines[nextLineIndex];
            const allLines = [];
            
            // 收集所有诗歌的所有诗句
            poems.forEach(poem => {
                poem.lines.forEach(line => {
                    allLines.push(line);
                });
            });
            
            // 过滤掉当前诗歌中已经出现的诗句
            const availableLines = allLines.filter(line => {
                return !currentPoem.lines.slice(0, nextLineIndex).includes(line) && line !== correctLine;
            });
            
            // 随机选择2个干扰项
            const wrongOptions = [];
            for (let i = 0; i < 2; i++) {
                const randomIndex = Math.floor(Math.random() * availableLines.length);
                wrongOptions.push(availableLines[randomIndex]);
                availableLines.splice(randomIndex, 1); // 确保不重复选择
            }
            
            // 组合选项并随机排序
            const options = [correctLine, ...wrongOptions];
            shuffleArray(options);
            
            // 创建选项按钮
            options.forEach(option => {
                const optionBtn = document.createElement('button');
                optionBtn.className = 'option-btn bg-secondary hover:bg-secondary/80 text-option font-semibold py-5 px-6 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg text-xl';
                optionBtn.textContent = option;
                optionBtn.addEventListener('click', () => handleOptionClick(optionBtn, option, correctLine));
                optionsContainer.appendChild(optionBtn);
            });
        }
        
        // 处理选项点击
        function handleOptionClick(button, selectedLine, correctLine) {
            // 禁用所有选项按钮
            document.querySelectorAll('#options-container button').forEach(btn => {
                btn.disabled = true;
            });
            
            // 播放音效
            if (selectedLine === correctLine) {
                playCorrectSound();
                button.classList.add('correct');
                
                // 延迟添加下一句
                setTimeout(() => {
                    gameState.currentLineIndex++;
                    addLineToDisplay(correctLine);
                    generateOptions();
                }, 800);
            } else {
                playWrongSound();
                button.classList.add('wrong');
                
                // 高亮显示正确答案
                document.querySelectorAll('#options-container button').forEach(btn => {
                    if (btn.textContent === correctLine) {
                        btn.classList.add('correct');
                    }
                });
                
                // 延迟添加下一句
                setTimeout(() => {
                    gameState.currentLineIndex++;
                    addLineToDisplay(correctLine);
                    generateOptions();
                }, 1500);
            }
        }
        
        // 开始计时
        function startTimer() {
            gameState.startTime = new Date();
            gameState.timerInterval = setInterval(() => {
                gameState.elapsedSeconds++;
                updateTimerDisplay();
            }, 1000);
        }
        
        // 更新计时器显示
        function updateTimerDisplay() {
            const minutes = Math.floor(gameState.elapsedSeconds / 60);
            const seconds = gameState.elapsedSeconds % 60;
            timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 游戏结束
        function endGame() {
            // 停止计时器
            clearInterval(gameState.timerInterval);
            
            // 显示胜利模态框
            const minutes = Math.floor(gameState.elapsedSeconds / 60);
            const seconds = gameState.elapsedSeconds % 60;
            completionTime.textContent = `用时: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            victoryModal.classList.remove('hidden');
            
            // 播放胜利音效
            playVictorySound();
        }
        
        // 工具函数：随机打乱数组
        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        }
        
        // 音效生成
        function playCorrectSound() {
            const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioCtx.createOscillator();
            const gainNode = audioCtx.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioCtx.destination);
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(600, audioCtx.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(800, audioCtx.currentTime + 0.3);
            
            gainNode.gain.setValueAtTime(0, audioCtx.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.5, audioCtx.currentTime + 0.05);
            gainNode.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.3);
            
            oscillator.start();
            oscillator.stop(audioCtx.currentTime + 0.3);
        }
        
        function playWrongSound() {
            const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioCtx.createOscillator();
            const gainNode = audioCtx.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioCtx.destination);
            
            oscillator.type = 'sawtooth';
            oscillator.frequency.setValueAtTime(200, audioCtx.currentTime);
            
            gainNode.gain.setValueAtTime(0, audioCtx.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioCtx.currentTime + 0.05);
            gainNode.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 0.5);
            
            oscillator.start();
            oscillator.stop(audioCtx.currentTime + 0.5);
        }
        
        function playVictorySound() {
            const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建多个音符
            [440, 523, 659, 880].forEach((freq, index) => {
                const oscillator = audioCtx.createOscillator();
                const gainNode = audioCtx.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioCtx.destination);
                
                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(freq, audioCtx.currentTime + index * 0.2);
                
                gainNode.gain.setValueAtTime(0, audioCtx.currentTime + index * 0.2);
                gainNode.gain.linearRampToValueAtTime(0.6, audioCtx.currentTime + index * 0.2 + 0.05);
                gainNode.gain.linearRampToValueAtTime(0, audioCtx.currentTime + index * 0.2 + 0.2);
                
                oscillator.start(audioCtx.currentTime + index * 0.2);
                oscillator.stop(audioCtx.currentTime + index * 0.2 + 0.2);
            });
        }
        
        // 页面加载完成后初始化游戏
        window.addEventListener('DOMContentLoaded', initGame);
    </script>
</body>
</html>
