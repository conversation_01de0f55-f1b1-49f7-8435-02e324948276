# 古诗学习乐园

## 项目介绍

古诗学习乐园是一个面向小学生的古诗学习平台，将四个独立的古诗学习游戏（古诗小侦探、古诗默写助手、诗影寻踪、连句成诗）整合到一个统一的页面中，并添加古诗选择功能，提供更完整、便捷的古诗学习体验。

## 主要功能

1. **四合一游戏平台**：在一个页面中集成四个古诗学习游戏，通过选项卡切换
2. **古诗选择功能**：可折叠的古诗选择面板，支持按年级、作者筛选
3. **数据统一管理**：统一的古诗数据库，便于维护和扩展
4. **响应式设计**：适配电脑、平板和手机等不同设备

## 游戏介绍

1. **古诗小侦探**：找出古诗中的错别字和错误用词
2. **古诗默写助手**：练习古诗默写，提高记忆能力
3. **诗影寻踪**：根据提示猜古诗，锻炼理解能力
4. **连句成诗**：将打乱的诗句重新排序，加深对诗歌结构的理解

## 技术架构

- 前端框架：原生JavaScript
- UI组件：Tailwind CSS
- 图标：Font Awesome
- 数据存储：localStorage

## 使用说明

1. 打开主页面 `古诗学习乐园.html`
2. 在顶部选项卡中选择想要玩的游戏
3. 使用左侧的古诗选择面板选择合适的古诗
4. 开始学习和游戏

## 项目结构

```
古诗学习乐园/
├── 古诗学习乐园.html    # 主页面
├── css/                # 样式文件
├── js/                 # JavaScript文件
│   ├── main.js         # 主要逻辑
│   ├── poems-data.js   # 古诗数据
│   └── games/          # 游戏逻辑
│       ├── detective.js  # 古诗小侦探
│       ├── recite.js     # 古诗默写助手
│       ├── shadow.js     # 诗影寻踪
│       └── connect.js    # 连句成诗
└── README.md           # 项目说明
```

## 开发团队

- 产品设计：初中生用户
- 技术实现：AI助手

## 版本历史

- v1.0.0 (2025-06-18): 初始版本，整合四个游戏，添加古诗选择功能
