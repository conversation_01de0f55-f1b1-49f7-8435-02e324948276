<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>古诗默写助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        paper: '#fdfaf3',
                        primary: '#6a5a4a',
                        highlight: '#5a8b82',
                        poemBg: '#f8f4ea',
                    },
                    fontFamily: {
                        kai: ['KaiTi', 'STKaiti', 'SimKai', 'serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .timer-shadow {
                text-shadow: 0 0 5px rgba(90, 139, 130, 0.5);
            }
            .blank-char {
                display: inline-block;
                width: 1.2em;
                height: 1.5em;
                text-align: center;
                position: relative;
                vertical-align: middle;
            }
            .blank-char::after {
                content: '_';
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                color: #6a5a4a;
                font-size: 1.5em;
                line-height: 1;
            }
            .char-reveal {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                text-align: center;
                color: #5a8b82;
                transform: translateY(100%);
                animation: revealChar 0.5s ease-out forwards;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                font-weight: bold;
                font-size: 1em;
            }
            .poem-center {
                text-align: center;
            }
            .poem-large {
                font-size: clamp(1.5rem, 4vw, 2.5rem); /* 进一步缩小字体范围 */
            }
            .poem-container {
                max-height: 70vh; /* 增加容器最大高度 */
                overflow-y: auto; /* 保留滚动条，但减少使用的可能性 */
            }
            .mobile-poem-container {
                max-height: none; /* 在移动设备上不限制高度 */
                overflow-y: visible;
            }
        }

        @keyframes revealChar {
            0% { transform: translateY(100%); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-paper min-h-screen flex flex-col items-center justify-center p-4 font-kai text-primary">
    <header class="w-full max-w-4xl mb-4 text-center">
        <h1 class="text-[clamp(1.8rem,5vw,3rem)] font-bold mb-2 tracking-wider">古诗默写助手</h1> <!-- 缩小标题字体 -->
        <p class="text-lg text-primary/80">线上出题，线下书写，即时核对</p>
    </header>

    <main class="w-full max-w-4xl flex-grow flex flex-col">
        <div class="bg-poemBg rounded-xl shadow-lg p-6 md:p-8 relative overflow-hidden">
            <div class="absolute top-4 right-4 opacity-10">
                <i class="fa fa-pencil-square-o text-5xl text-primary"></i> <!-- 缩小装饰图标 -->
            </div>
            
            <div id="poem-container" class="relative z-10 poem-container mobile-poem-container">
                <div id="poem-header" class="mb-4 text-center">
                    <h2 id="poem-title" class="text-[clamp(1.5rem,4vw,2.5rem)] font-bold mb-2">《晓出净慈寺送林子方》</h2> <!-- 统一缩小标题字体 -->
                    <p id="poem-author" class="text-lg text-primary/70">宋·杨万里</p> <!-- 缩小作者字体 -->
                </div>
                
                <div id="poem-content" class="space-y-4 leading-relaxed poem-center poem-large"> <!-- 减少行间距 -->
                    <!-- 诗句会通过JS动态生成 -->
                </div>
            </div>
        </div>

        <div class="mt-6 flex flex-col md:flex-row items-center justify-between gap-4">
            <div id="timer" class="bg-white rounded-xl shadow-lg p-3 text-center w-full md:w-auto">
                <i class="fa fa-clock-o mr-2 text-primary/70 text-lg"></i> <!-- 缩小计时器图标 -->
                <span class="text-lg font-mono timer-shadow">00:00</span> <!-- 缩小计时器字体 -->
            </div>
            
            <div class="flex gap-3 w-full md:w-auto">
                <button id="next-btn" class="bg-primary hover:bg-primary/90 text-white px-5 py-2.5 rounded-xl shadow-lg transition-all transform hover:scale-105 focus:outline-none focus:ring-3 focus:ring-primary/50 text-sm">
                    <i class="fa fa-refresh mr-2"></i>换一首
                </button>
                <button id="check-btn" class="bg-highlight hover:bg-highlight/90 text-white px-5 py-2.5 rounded-xl shadow-lg transition-all transform hover:scale-105 focus:outline-none focus:ring-3 focus:ring-highlight/50 text-sm" disabled>
                    <i class="fa fa-check-circle mr-2"></i>核对答案
                </button>
            </div>
        </div>
    </main>

    <footer class="mt-8 text-center text-primary/60 text-xs"> <!-- 缩小页脚字体 -->
        <p>专注古诗词学习 · 助力传统文化传承</p>
    </footer>

    <script>
        // 内置古诗数据
        const poems = [
            {
                title: "《村居》",
                author: "清·高鼎",
                content: "草长莺飞二月天，拂堤杨柳醉春烟。儿童散学归来早，忙趁东风放纸鸢。"
            },
            {
                title: "《咏柳》",
                author: "唐·贺知章",
                content: "碧玉妆成一树高，万条垂下绿丝绦。不知细叶谁裁出，二月春风似剪刀。"
            },
            {
                title: "《赋得古原草送别》",
                author: "唐·白居易",
                content: "离离原上草，一岁一枯荣。野火烧不尽，春风吹又生。"
            },
            {
                title: "《晓出净慈寺送林子方》",
                author: "宋·杨万里",
                content: "毕竟西湖六月中，风光不与四时同。接天莲叶无穷碧，映日荷花别样红。"
            },
            {
                title: "《绝句》",
                author: "唐·杜甫",
                content: "两个黄鹂鸣翠柳，一行白鹭上青天。窗含西岭千秋雪，门泊东吴万里船。"
            },
            {
                title: "《悯农（其一）》",
                author: "唐·李绅",
                content: "春种一粒粟，秋收万颗子。四海无闲田，农夫犹饿死。"
            },
            {
                title: "《舟夜书所见》",
                author: "清·查慎行",
                content: "月黑见渔灯，孤光一点萤。微微风簇浪，散作满河星。"
            }
        ];

        // DOM元素
        const poemTitle = document.getElementById('poem-title');
        const poemAuthor = document.getElementById('poem-author');
        const poemContent = document.getElementById('poem-content');
        const nextBtn = document.getElementById('next-btn');
        const checkBtn = document.getElementById('check-btn');
        const timer = document.getElementById('timer').querySelector('span');
        const poemContainer = document.getElementById('poem-container');

        // 计时器变量
        let startTime = null;
        let timerInterval = null;
        let isTimerRunning = false;

        // 检测是否为移动设备
        function isMobileDevice() {
            return window.innerWidth < 768; // 以768px为界限区分移动设备
        }

        // 随机选择一首诗
        function selectRandomPoem() {
            const randomIndex = Math.floor(Math.random() * poems.length);
            return poems[randomIndex];
        }

        // 处理诗句挖空
        function processPoemLines(poem) {
            const lines = poem.content.split(/([，。？！；,.?!;])/g).filter(line => line.trim() !== '');
            
            let processedLines = [];
            let currentLine = '';
            let currentPunctuation = '';
            
            lines.forEach((segment, index) => {
                // 判断是否为标点符号
                const isPunctuation = /[，。？！；,.?!;]/.test(segment);
                
                if (isPunctuation) {
                    currentPunctuation = segment;
                    
                    // 处理当前行
                    const lineLength = currentLine.length;
                    const halfLength = Math.ceil(lineLength / 2);
                    
                    // 随机选择前半部分或后半部分挖空
                    const shouldBlankFirstHalf = Math.random() > 0.5;
                    let blankedLine = '';
                    let answer = '';
                    
                    if (shouldBlankFirstHalf) {
                        answer = currentLine.substring(0, halfLength);
                        blankedLine = '_'.repeat(halfLength) + currentLine.substring(halfLength);
                    } else {
                        answer = currentLine.substring(halfLength);
                        blankedLine = currentLine.substring(0, halfLength) + '_'.repeat(lineLength - halfLength);
                    }
                    
                    processedLines.push({
                        original: currentLine + currentPunctuation,
                        blanked: blankedLine + currentPunctuation,
                        answer: answer,
                        blankPosition: shouldBlankFirstHalf ? 'first' : 'second',
                        blankLength: shouldBlankFirstHalf ? halfLength : lineLength - halfLength,
                        fullLineLength: lineLength,
                        punctuation: currentPunctuation
                    });
                    
                    // 重置当前行和标点
                    currentLine = '';
                    currentPunctuation = '';
                } else {
                    currentLine += segment;
                }
            });
            
            return processedLines;
        }

        // 创建下划线占位符
        function createBlankChars(length) {
            let html = '';
            for (let i = 0; i < length; i++) {
                html += `<span class="blank-char" data-index="${i}"></span>`;
            }
            return html;
        }

        // 显示带挖空的诗句
        function displayBlankedPoem(processedLines) {
            poemContent.innerHTML = '';
            
            processedLines.forEach(line => {
                const lineElement = document.createElement('p');
                lineElement.className = 'line relative mx-auto';
                
                if (line.blankPosition === 'first') {
                    // 前半部分挖空
                    let lineHTML = '';
                    lineHTML += `<span class="blank" data-answer="${line.answer}">${createBlankChars(line.blankLength)}</span>`;
                    lineHTML += line.blanked.substring(line.blankLength);
                    lineElement.innerHTML = lineHTML;
                } else {
                    // 后半部分挖空
                    let lineHTML = '';
                    lineHTML += line.blanked.substring(0, line.fullLineLength - line.blankLength);
                    lineHTML += `<span class="blank" data-answer="${line.answer}">${createBlankChars(line.blankLength)}</span>`;
                    lineHTML += line.punctuation; // 确保标点符号始终存在
                    lineElement.innerHTML = lineHTML;
                }
                
                poemContent.appendChild(lineElement);
            });
            
            // 调整容器高度
            adjustContainerHeight();
        }

        // 调整容器高度
        function adjustContainerHeight() {
            if (isMobileDevice()) {
                poemContainer.classList.add('mobile-poem-container');
            } else {
                poemContainer.classList.remove('mobile-poem-container');
            }
        }

        // 显示完整诗句（核对答案）
        function displayFullPoem() {
            const blankElements = document.querySelectorAll('.blank');
            
            blankElements.forEach((blank, blankIndex) => {
                const answer = blank.dataset.answer;
                const blankChars = blank.querySelectorAll('.blank-char');
                
                answer.split('').forEach((char, charIndex) => {
                    if (blankChars[charIndex]) {
                        setTimeout(() => {
                            const charReveal = document.createElement('span');
                            charReveal.className = 'char-reveal';
                            charReveal.dataset.char = char;
                            charReveal.textContent = char;
                            blankChars[charIndex].appendChild(charReveal);
                            
                            // 触发动画
                            setTimeout(() => {
                                charReveal.style.opacity = '1';
                                charReveal.style.transform = 'translateY(0)';
                            }, 10);
                        }, (blankIndex * 300) + (charIndex * 150));
                    }
                });
            });
            
            stopTimer();
            checkBtn.disabled = true;
        }

        // 开始新的一轮
        function startNewRound() {
            // 重置计时器
            stopTimer();
            timer.textContent = '00:00';
            
            // 选择随机诗歌
            const poem = selectRandomPoem();
            
            // 更新标题和作者
            poemTitle.textContent = poem.title;
            poemAuthor.textContent = poem.author;
            
            // 处理并显示诗句
            const processedLines = processPoemLines(poem);
            displayBlankedPoem(processedLines);
            
            // 启用核对按钮
            checkBtn.disabled = false;
            
            // 开始计时
            startTimer();
        }

        // 计时器功能
        function startTimer() {
            if (isTimerRunning) return;
            
            startTime = new Date();
            isTimerRunning = true;
            
            timerInterval = setInterval(() => {
                const currentTime = new Date();
                const elapsedTime = Math.floor((currentTime - startTime) / 1000);
                
                const minutes = Math.floor(elapsedTime / 60).toString().padStart(2, '0');
                const seconds = (elapsedTime % 60).toString().padStart(2, '0');
                
                timer.textContent = `${minutes}:${seconds}`;
            }, 1000);
        }

        function stopTimer() {
            if (!isTimerRunning) return;
            
            clearInterval(timerInterval);
            isTimerRunning = false;
        }

        // 添加动画效果
        function addAnimations() {
            // 页面加载动画
            document.body.classList.add('opacity-0');
            setTimeout(() => {
                document.body.classList.add('transition-opacity', 'duration-700');
                document.body.classList.remove('opacity-0');
            }, 100);
            
            // 按钮悬停效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => {
                btn.addEventListener('mouseenter', () => {
                    btn.classList.add('shadow-xl');
                });
                btn.addEventListener('mouseleave', () => {
                    btn.classList.remove('shadow-xl');
                });
            });
        }

        // 初始化事件监听器
        function initEventListeners() {
            nextBtn.addEventListener('click', startNewRound);
            checkBtn.addEventListener('click', displayFullPoem);
            
            // 监听窗口大小变化，调整容器高度
            window.addEventListener('resize', adjustContainerHeight);
        }

        // 初始化应用
        function initApp() {
            addAnimations();
            initEventListeners();
            startNewRound();
        }

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>    