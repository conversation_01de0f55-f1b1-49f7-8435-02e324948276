<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>古诗小侦探</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        parchment: '#f9f5f0', // 更浅的纯色背景
                        brown: '#6d5b4b',
                        gold: '#d4af37',
                        green: '#2e7d32',
                        red: '#c62828'
                    },
                    fontFamily: {
                        song: ['"Noto Serif SC"', 'serif']
                    },
                    boxShadow: {
                        'inner-lg': 'inset 0 2px 10px 0 rgba(0, 0, 0, 0.1)',
                        'parchment': '0 10px 25px -5px rgba(109, 91, 75, 0.1), 0 8px 10px -6px rgba(109, 91, 75, 0.1)'
                    }
                }
            }
        }
    </script>

    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            }
            .text-shadow-lg {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            }
            .highlight-find {
                animation: highlight 1.5s ease-in-out;
            }
            @keyframes highlight {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }
            .error-found {
                text-decoration: underline wavy #2e7d32;
            }
            .error-highlight {
                text-decoration: underline wavy #c62828;
            }
            .fade-in {
                animation: fadeIn 0.5s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .btn-hover {
                transition: all 0.2s ease-in-out;
            }
            .btn-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }
        }
    </style>
</head>
<body class="bg-parchment min-h-screen flex flex-col items-center justify-center p-4 md:p-8 font-song text-brown">
    <!-- 游戏容器 -->
    <div id="game-container" class="max-w-3xl w-full mx-auto">
        <!-- 游戏标题与侦探头像 -->
        <div class="text-center mb-8 fade-in flex flex-col items-center">
            <div class="flex items-center justify-center mb-4">
                <h1 class="text-[clamp(2rem,5vw,3rem)] font-bold text-brown text-shadow-lg mr-4">
                    <span class="text-gold">古</span><span class="text-brown">诗</span>
                    <span class="text-gold">小</span><span class="text-brown">侦</span><span class="text-gold">探</span>
                </h1>
                <!-- 侦探头像 - 调整为与标题字体相同大小 -->
                <i class="fa fa-user-secret text-[clamp(2rem,5vw,3rem)] text-gold shadow-lg"></i>
            </div>
            <p class="text-[clamp(1rem,2vw,1.25rem)] mt-2 opacity-80">寻找古诗中的错别字，成为汉字小专家！</p>
        </div>

        <!-- 开始界面 -->
        <div id="start-screen" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-parchment p-8 md:p-12 fade-in">
            <div class="text-center mb-8">
                <i class="fa fa-search text-5xl text-gold mb-4"></i>
                <h2 class="text-2xl md:text-3xl font-bold mb-4">欢迎来到古诗小侦探</h2>
                <p class="text-lg opacity-80 mb-6">🕵️ 在这个游戏中，你需要找出古诗中隐藏的错误字符。错误已完全隐藏，没有任何提示，需要你仔细观察！准备好了吗？</p>
            </div>
            <div class="flex justify-center">
                <button id="start-btn" class="bg-gold hover:bg-gold/90 text-white font-bold py-3 px-8 rounded-full text-lg shadow-lg btn-hover transition-all duration-300 active:scale-95">
                    <i class="fa fa-book mr-2"></i>开始调查
                </button>
            </div>
        </div>

        <!-- 游戏界面 -->
        <div id="game-screen" class="hidden">
            <!-- 诗歌容器 -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-parchment p-6 md:p-10 mb-6 fade-in">
                <!-- 诗歌信息 -->
                <div class="text-center mb-6">
                    <h2 id="poem-title" class="text-2xl md:text-3xl font-bold mb-2"></h2>
                    <p id="poem-author" class="text-lg opacity-80"></p>
                </div>

                <!-- 诗歌文本 -->
                <div id="poem-text" class="text-2xl md:text-3xl leading-relaxed space-y-4">
                    <!-- 诗歌内容将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 控制区 -->
            <div class="flex flex-wrap justify-center gap-4">
                <button id="help-btn" class="bg-red/90 hover:bg-red text-white font-bold py-3 px-6 rounded-full text-lg shadow-lg btn-hover flex items-center">
                    <i class="fa fa-search-plus mr-2"></i>求助
                </button>
                <button id="restart-btn" class="bg-brown/90 hover:bg-brown text-white font-bold py-3 px-6 rounded-full text-lg shadow-lg btn-hover flex items-center">
                    <i class="fa fa-refresh mr-2"></i>重新开始
                </button>
            </div>
        </div>

        <!-- 修复弹窗 -->
        <div id="fix-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden fade-in">
            <div class="bg-parchment rounded-2xl shadow-xl p-6 md:p-10 max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold mb-2">发现错误！</h3>
                    <p id="error-type" class="text-lg opacity-80"></p>
                </div>
                <div class="mb-6">
                    <p class="text-lg font-bold mb-2">请选择正确的汉字：</p>
                    <div id="fix-options" class="grid grid-cols-2 gap-3"></div>
                </div>
                <div class="flex justify-center">
                    <button id="close-fix-btn" class="bg-brown/80 hover:bg-brown text-white font-bold py-2 px-6 rounded-full text-lg shadow-md btn-hover">
                        取消
                    </button>
                </div>
            </div>
        </div>

        <!-- 胜利弹窗 -->
        <div id="victory-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden fade-in">
            <div class="bg-parchment rounded-2xl shadow-xl p-6 md:p-10 max-w-md w-full mx-4 text-center">
                <div class="mb-6">
                    <i class="fa fa-trophy text-6xl text-gold mb-4"></i>
                    <h3 class="text-2xl font-bold mb-2">恭喜你！</h3>
                    <p class="text-lg opacity-80 mb-4">你成功找出了所有错误！</p>
                </div>
                <div class="flex justify-center">
                    <button id="next-btn" class="bg-gold hover:bg-gold/90 text-white font-bold py-3 px-8 rounded-full text-lg shadow-lg btn-hover">
                        <i class="fa fa-arrow-right mr-2"></i>下一首
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="mt-8 text-center text-sm opacity-60">
        <p>古诗小侦探 &copy; 2023 | 寓教于乐，传承经典</p>
    </footer>

    <script>
        // 诗歌数据
        const poems = [
            {
                title: "村居",
                author: "清·高鼎",
                content: "草长莺飞二月天，拂堤杨柳醉春烟。儿童散学归来早，忙趁东风放纸鸢。",
                errors: {
                    homophones: { "莺": "鹰", "醉": "最", "鸢": "鸳" },
                    similar: { "堤": "提", "杨": "扬", "鸢": "鸳" },
                    reverse: ["东风", "纸鸢"]
                }
            },
            {
                title: "咏柳",
                author: "唐·贺知章",
                content: "碧玉妆成一树高，万条垂下绿丝绦。不知细叶谁裁出，二月春风似剪刀。",
                errors: {
                    homophones: { "妆": "装", "绦": "涛", "裁": "才" },
                    similar: { "碧": "壁", "绦": "条", "剪": "箭" },
                    reverse: ["春风", "剪刀"]
                }
            },
            {
                title: "赋得古原草送别",
                author: "唐·白居易",
                content: "离离原上草，一岁一枯荣。野火烧不尽，春风吹又生。",
                errors: {
                    homophones: { "离": "梨", "岁": "穗", "尽": "进" },
                    similar: { "原": "园", "荣": "菜", "烧": "浇" },
                    reverse: ["春风", "野火"]
                }
            },
            {
                title: "晓出净慈寺送林子方",
                author: "宋·杨万里",
                content: "毕竟西湖六月中，风光不与四时同。接天莲叶无穷碧，映日荷花别样红。",
                errors: {
                    homophones: { "毕": "必", "竟": "镜", "莲": "连" },
                    similar: { "竟": "竞", "碧": "壁", "映": "应" },
                    reverse: ["西湖", "荷花"]
                }
            },
            {
                title: "绝句",
                author: "唐·杜甫",
                content: "两个黄鹂鸣翠柳，一行白鹭上青天。窗含西岭千秋雪，门泊东吴万里船。",
                errors: {
                    homophones: { "鹂": "梨", "鹭": "路", "泊": "博" },
                    similar: { "鹭": "鹜", "岭": "领", "窗": "囱" },
                    reverse: ["黄鹂", "东吴"]
                }
            },
            {
                title: "悯农（其一）",
                author: "唐·李绅",
                content: "春种一粒粟，秋收万颗子。四海无闲田，农夫犹饿死。",
                errors: {
                    homophones: { "粟": "素", "颗": "棵", "闲": "贤" },
                    similar: { "粟": "栗", "颗": "棵", "犹": "优" },
                    reverse: ["秋收", "四海"]
                }
            },
            {
                title: "舟夜书所见",
                author: "清·查慎行",
                content: "月黑见渔灯，孤光一点萤。微微风簇浪，散作满河星。",
                errors: {
                    homophones: { "渔": "鱼", "簇": "促", "作": "坐" },
                    similar: { "渔": "鱼", "萤": "荧", "簇": "族" },
                    reverse: ["渔灯", "满河"]
                }
            }
        ];

        // 游戏状态
        let currentPoem = null;
        let errors = [];
        let foundErrors = [];
        let currentSelectedError = null;

        // DOM元素
        const startScreen = document.getElementById('start-screen');
        const gameScreen = document.getElementById('game-screen');
        const victoryModal = document.getElementById('victory-modal');
        const fixModal = document.getElementById('fix-modal');
        const poemTitle = document.getElementById('poem-title');
        const poemAuthor = document.getElementById('poem-author');
        const poemText = document.getElementById('poem-text');
        const startBtn = document.getElementById('start-btn');
        const helpBtn = document.getElementById('help-btn');
        const restartBtn = document.getElementById('restart-btn');
        const nextBtn = document.getElementById('next-btn');
        const closeFixBtn = document.getElementById('close-fix-btn');
        const errorType = document.getElementById('error-type');
        const fixOptions = document.getElementById('fix-options');

        // 初始化游戏
        function initGame() {
            console.log('游戏初始化开始');
            // 随机选择一首诗
            currentPoem = poems[Math.floor(Math.random() * poems.length)];

            // 重置游戏状态
            errors = [];
            foundErrors = [];
            currentSelectedError = null;

            // 显示诗歌信息
            poemTitle.textContent = currentPoem.title;
            poemAuthor.textContent = currentPoem.author;

            // 生成错误
            generateErrors();

            // 渲染诗歌
            renderPoem();

            // 显示游戏界面
            startScreen.classList.add('hidden');
            gameScreen.classList.remove('hidden');
            victoryModal.classList.add('hidden');
            fixModal.classList.add('hidden');

            console.log('游戏初始化完成');
        }

        // 生成错误
        function generateErrors() {
            // 决定错误数量 (2-3个)
            const errorCount = Math.floor(Math.random() * 2) + 2;

            // 可用的错误类型
            const errorTypes = ['homophones', 'similar', 'reverse'];

            // 已经使用的位置
            const usedPositions = new Set();

            // 生成错误
            for (let i = 0; i < errorCount; i++) {
                // 随机选择错误类型
                const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)];

                // 根据错误类型生成错误
                if (errorType === 'reverse') {
                    // 词序颠倒错误
                    const reverseWords = currentPoem.errors.reverse;
                    if (reverseWords.length > 0) {
                        const randomIndex = Math.floor(Math.random() * reverseWords.length);
                        const word = reverseWords[randomIndex];
                        const position = currentPoem.content.indexOf(word);

                        if (position !== -1 && !usedPositions.has(position)) {
                            errors.push({
                                type: 'reverse',
                                original: word,
                                error: word.split('').reverse().join(''),
                                position: position
                            });
                            usedPositions.add(position);
                        }
                    }
                } else {
                    // 同音字或形近字错误
                    const errorMap = currentPoem.errors[errorType];
                    const validChars = Object.keys(errorMap);

                    if (validChars.length > 0) {
                        const randomIndex = Math.floor(Math.random() * validChars.length);
                        const originalChar = validChars[randomIndex];
                        const errorChar = errorMap[originalChar];

                        // 查找字符在诗中的位置
                        const positions = [];
                        for (let j = 0; j < currentPoem.content.length; j++) {
                            if (currentPoem.content[j] === originalChar) {
                                positions.push(j);
                            }
                        }

                        if (positions.length > 0) {
                            // 随机选择一个位置
                            const positionIndex = Math.floor(Math.random() * positions.length);
                            const position = positions[positionIndex];

                            if (!usedPositions.has(position)) {
                                errors.push({
                                    type: errorType === 'homophones' ? 'homophone' : 'similar',
                                    original: originalChar,
                                    error: errorChar,
                                    position: position
                                });
                                usedPositions.add(position);
                            }
                        }
                    }
                }
            }
        }

        // 渲染诗歌
        function renderPoem() {
            // 清空诗歌容器
            poemText.innerHTML = '';

            // 复制诗歌内容用于修改
            let modifiedContent = currentPoem.content;

            // 按错误位置降序排序，以便正确替换
            errors.sort((a, b) => b.position - a.position);

            // 应用错误
            for (const error of errors) {
                if (error.type === 'reverse') {
                    // 词序颠倒
                    modifiedContent = modifiedContent.substring(0, error.position) +
                                     error.error +
                                     modifiedContent.substring(error.position + error.original.length);
                } else {
                    // 同音字或形近字
                    modifiedContent = modifiedContent.substring(0, error.position) +
                                     error.error +
                                     modifiedContent.substring(error.position + 1);
                }
            }

            // 分割成行（以逗号、句号、问号、感叹号为分隔符）
            const lines = modifiedContent.split(/[，。？！]/).filter(line => line.trim() !== '');
            const punctuation = modifiedContent.match(/[，。？！]/g) || [];

            // 渲染每一行
            lines.forEach((line, index) => {
                const lineElement = document.createElement('div');
                lineElement.className = 'text-center';

                // 渲染每个字符
                for (let i = 0; i < line.length; i++) {
                    const char = line[i];
                    const charElement = document.createElement('span');
                    charElement.textContent = char;
                    charElement.className = 'mx-1 cursor-pointer inline-block transition-all duration-200 hover:scale-110';

                    // 为错误字符添加点击事件
                    const position = modifiedContent.indexOf(line) + i;
                    const error = errors.find(e => e.position === position);

                    if (error) {
                        // 检查是否已找到此错误
                        const found = foundErrors.find(e => e.position === position);

                        if (found) {
                            // 已修复的错误
                            charElement.textContent = found.original;
                            charElement.className = 'mx-1 inline-block error-found';
                        } else {
                            // 未发现的错误
                            charElement.dataset.errorPosition = position;
                            charElement.addEventListener('click', () => selectError(error));
                        }
                    }

                    lineElement.appendChild(charElement);
                }

                // 添加标点符号
                if (index < punctuation.length) {
                    const punctElement = document.createElement('span');
                    punctElement.textContent = punctuation[index];
                    punctElement.className = 'mx-1';
                    lineElement.appendChild(punctElement);
                }

                poemText.appendChild(lineElement);
            });
        }

        // 选择错误
        function selectError(error) {
            currentSelectedError = error;

            // 显示错误类型
            let typeText = '';
            switch (error.type) {
                case 'homophone':
                    typeText = '这是一个同音字错误';
                    break;
                case 'similar':
                    typeText = '这是一个形近字错误';
                    break;
                case 'reverse':
                    typeText = '这是一个词序颠倒错误';
                    break;
            }

            errorType.textContent = typeText;

            // 生成修复选项
            fixOptions.innerHTML = '';

            // 正确选项
            const correctOption = document.createElement('button');
            correctOption.textContent = error.original;
            correctOption.className = 'bg-green text-white font-bold py-2 px-4 rounded-lg shadow-md btn-hover';
            correctOption.addEventListener('click', () => fixError(true));
            fixOptions.appendChild(correctOption);

            // 错误选项
            const wrongOption = document.createElement('button');
            wrongOption.textContent = error.error;
            wrongOption.className = 'bg-red/80 text-white font-bold py-2 px-4 rounded-lg shadow-md btn-hover';
            wrongOption.addEventListener('click', () => fixError(false));
            fixOptions.appendChild(wrongOption);

            // 随机打乱选项顺序
            const options = Array.from(fixOptions.children);
            fixOptions.innerHTML = '';
            while (options.length) {
                const randomIndex = Math.floor(Math.random() * options.length);
                fixOptions.appendChild(options.splice(randomIndex, 1)[0]);
            }

            // 显示修复弹窗
            fixModal.classList.remove('hidden');
        }

        // 修复错误
        function fixError(isCorrect) {
            if (isCorrect && currentSelectedError) {
                // 添加到已修复错误列表
                foundErrors.push(currentSelectedError);

                // 播放正确音效
                playCorrectSound();

                // 高亮显示修复的字符
                highlightFoundError(currentSelectedError.position);

                // 检查是否所有错误都已修复
                if (foundErrors.length === errors.length) {
                    // 延迟显示胜利弹窗
                    setTimeout(() => {
                        victoryModal.classList.remove('hidden');
                    }, 1000);
                }
            } else {
                // 播放错误音效
                playErrorSound();
            }

            // 关闭修复弹窗
            fixModal.classList.add('hidden');
            currentSelectedError = null;
        }

        // 高亮显示已找到的错误
        function highlightFoundError(position) {
            // 找到对应的字符元素
            const charElements = poemText.querySelectorAll('span');
            charElements.forEach(el => {
                if (el.dataset.errorPosition === position.toString()) {
                    // 更新为正确的字符
                    el.textContent = errors.find(e => e.position === position).original;

                    // 添加已找到的样式
                    el.classList.add('error-found');
                    el.classList.remove('cursor-pointer');

                    // 添加高亮动画
                    el.classList.add('highlight-find');

                    // 移除点击事件
                    el.removeEventListener('click', () => selectError(errors.find(e => e.position === position)));
                }
            });
        }

        // 求助功能
        function useHelp() {
            // 找到一个未被发现的错误
            const remainingErrors = errors.filter(e => !foundErrors.some(fe => fe.position === e.position));

            if (remainingErrors.length > 0) {
                // 随机选择一个错误
                const randomIndex = Math.floor(Math.random() * remainingErrors.length);
                const errorToReveal = remainingErrors[randomIndex];

                // 高亮显示错误
                const charElements = poemText.querySelectorAll('span');
                charElements.forEach(el => {
                    if (el.dataset.errorPosition === errorToReveal.position.toString()) {
                        el.classList.add('error-highlight');

                        // 移除点击事件
                        el.removeEventListener('click', () => selectError(errorToReveal));

                        // 播放提示音效
                        playHintSound();
                    }
                });
            }
        }

        // 播放正确音效
        function playCorrectSound() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(880, audioContext.currentTime + 0.3);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);

            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.5);
        }

        // 播放错误音效
        function playErrorSound() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.type = 'sawtooth';
            oscillator.frequency.setValueAtTime(220, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(110, audioContext.currentTime + 0.3);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5);

            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.5);
        }

        // 播放提示音效
        function playHintSound() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(330, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(330, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(293.66, audioContext.currentTime + 0.2);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);

            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.3);
        }

        // 事件监听
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，添加事件监听');

            startBtn.addEventListener('click', function() {
                console.log('开始按钮被点击');
                initGame();
            });

            helpBtn.addEventListener('click', useHelp);
            restartBtn.addEventListener('click', initGame);
            nextBtn.addEventListener('click', initGame);
            closeFixBtn.addEventListener('click', function() {
                fixModal.classList.add('hidden');
                currentSelectedError = null;
            });

            // 添加按钮点击效果
            const buttons = document.querySelectorAll('.btn-hover');
            buttons.forEach(btn => {
                btn.addEventListener('mousedown', function() {
                    this.classList.add('scale-95');
                });
                btn.addEventListener('mouseup', function() {
                    this.classList.remove('scale-95');
                });
                btn.addEventListener('mouseleave', function() {
                    this.classList.remove('scale-95');
                });
            });
        });

        // 初始化游戏
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            // 添加淡入效果
            startScreen.classList.add('fade-in');
        });
    </script>
</body>
</html>
