<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>古诗学习乐园</title>
    <!-- 引入 Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- 引入 Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* 自定义样式 */
        .tab-active {
            border-bottom: 3px solid #4f46e5;
            color: #4f46e5;
        }
        .poem-panel-collapsed {
            width: 50px;
            overflow: hidden;
        }
        .poem-panel-expanded {
            width: 300px;
        }
        .game-container {
            min-height: 500px;
        }
        /* 过渡动画 */
        .transition-all {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- 页眉 -->
    <header class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-book-open text-2xl mr-3"></i>
                <h1 class="text-2xl font-bold">古诗学习乐园</h1>
            </div>
            <div class="text-sm">
                让学习古诗更有趣
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="flex-grow container mx-auto p-4 flex flex-col md:flex-row">
        <!-- 古诗选择面板（可折叠） -->
        <div id="poem-selection-panel" class="poem-panel-expanded bg-white rounded-lg shadow-md p-4 mr-4 mb-4 md:mb-0 transition-all">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-bold text-gray-700">古诗选择</h2>
                <button id="toggle-panel-btn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <!-- 筛选选项 -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="grade-filter">
                    年级筛选
                </label>
                <select id="grade-filter" class="w-full px-3 py-2 border rounded-md">
                    <option value="all">全部年级</option>
                    <option value="1">一年级</option>
                    <option value="2">二年级</option>
                    <option value="3">三年级</option>
                    <option value="4">四年级</option>
                    <option value="5">五年级</option>
                    <option value="6">六年级</option>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="author-filter">
                    作者筛选
                </label>
                <select id="author-filter" class="w-full px-3 py-2 border rounded-md">
                    <option value="all">全部作者</option>
                    <!-- 作者列表将通过JavaScript动态生成 -->
                </select>
            </div>
            
            <!-- 古诗列表 -->
            <div class="mb-4 overflow-y-auto max-h-64">
                <h3 class="text-gray-700 text-sm font-bold mb-2">古诗列表</h3>
                <ul id="poem-list" class="space-y-2">
                    <!-- 古诗列表将通过JavaScript动态生成 -->
                    <li class="flex items-center">
                        <input type="checkbox" class="mr-2" id="poem-1">
                        <label for="poem-1" class="text-gray-600 text-sm">登鹳雀楼 - 王之涣</label>
                    </li>
                    <li class="flex items-center">
                        <input type="checkbox" class="mr-2" id="poem-2">
                        <label for="poem-2" class="text-gray-600 text-sm">春晓 - 孟浩然</label>
                    </li>
                    <!-- 更多古诗... -->
                </ul>
            </div>
            
            <!-- 选择按钮 -->
            <div class="flex space-x-2">
                <button class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm flex-1">
                    应用选择
                </button>
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md text-sm">
                    随机选择
                </button>
            </div>
            
            <!-- 导入导出按钮 -->
            <div class="mt-4 flex space-x-2">
                <button class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-sm flex-1">
                    <i class="fas fa-file-import mr-1"></i> 导入古诗
                </button>
                <button class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-md text-sm flex-1">
                    <i class="fas fa-file-export mr-1"></i> 导出选择
                </button>
            </div>
        </div>

        <!-- 游戏内容区 -->
        <div class="flex-grow flex flex-col">
            <!-- 游戏导航选项卡 -->
            <div class="flex border-b mb-4">
                <button class="tab-active px-4 py-2 font-medium text-sm">
                    <i class="fas fa-search mr-1"></i> 古诗小侦探
                </button>
                <button class="px-4 py-2 font-medium text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-pen mr-1"></i> 古诗默写助手
                </button>
                <button class="px-4 py-2 font-medium text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-puzzle-piece mr-1"></i> 诗影寻踪
                </button>
                <button class="px-4 py-2 font-medium text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-sort mr-1"></i> 连句成诗
                </button>
            </div>
            
            <!-- 游戏内容 -->
            <div class="game-container bg-white rounded-lg shadow-md p-6 flex-grow">
                <!-- 这里将根据选择的游戏动态加载内容 -->
                <div id="game-detective" class="text-center">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">古诗小侦探</h2>
                    <p class="text-gray-600 mb-6">在这个游戏中，你需要找出古诗中的错别字和错误用词。</p>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <p class="text-lg mb-2">请选择左侧的古诗，然后开始游戏！</p>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                            开始游戏
                        </button>
                    </div>
                    <div class="text-gray-500 text-sm">
                        <p>游戏规则：</p>
                        <p>1. 系统会在古诗中故意加入一些错误</p>
                        <p>2. 你需要找出这些错误并点击它们</p>
                        <p>3. 找出所有错误即可获胜</p>
                    </div>
                </div>
                
                <!-- 其他游戏内容将被隐藏，通过JavaScript切换显示 -->
                <div id="game-recite" class="hidden">
                    <!-- 古诗默写助手内容 -->
                </div>
                <div id="game-shadow" class="hidden">
                    <!-- 诗影寻踪内容 -->
                </div>
                <div id="game-connect" class="hidden">
                    <!-- 连句成诗内容 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white p-4 mt-8">
        <div class="container mx-auto text-center">
            <p>古诗学习乐园 &copy; 2025 | 让学习古诗更有趣</p>
            <div class="mt-2 text-gray-400 text-sm">
                <a href="#" class="hover:text-white mx-2">关于我们</a>
                <a href="#" class="hover:text-white mx-2">使用帮助</a>
                <a href="#" class="hover:text-white mx-2">联系方式</a>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 折叠/展开古诗选择面板
        document.getElementById('toggle-panel-btn').addEventListener('click', function() {
            const panel = document.getElementById('poem-selection-panel');
            const icon = this.querySelector('i');
            
            if (panel.classList.contains('poem-panel-expanded')) {
                panel.classList.remove('poem-panel-expanded');
                panel.classList.add('poem-panel-collapsed');
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-right');
            } else {
                panel.classList.remove('poem-panel-collapsed');
                panel.classList.add('poem-panel-expanded');
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
            }
        });
        
        // 游戏选项卡切换
        const tabs = document.querySelectorAll('main .flex.border-b button');
        const gameContainers = [
            document.getElementById('game-detective'),
            document.getElementById('game-recite'),
            document.getElementById('game-shadow'),
            document.getElementById('game-connect')
        ];
        
        tabs.forEach((tab, index) => {
            tab.addEventListener('click', function() {
                // 移除所有选项卡的活动状态
                tabs.forEach(t => t.classList.remove('tab-active'));
                // 添加当前选项卡的活动状态
                this.classList.add('tab-active');
                
                // 隐藏所有游戏内容
                gameContainers.forEach(container => {
                    if (container) container.classList.add('hidden');
                });
                
                // 显示当前选择的游戏内容
                if (gameContainers[index]) {
                    gameContainers[index].classList.remove('hidden');
                }
            });
        });
        
        // 这里将添加更多功能，如古诗数据加载、游戏逻辑等
    </script>
</body>
</html>
