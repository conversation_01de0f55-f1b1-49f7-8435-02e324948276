/**
 * 古诗学习乐园 - 古诗数据
 * 包含所有古诗的基本信息：标题、作者、年级、内容
 */

// 二年级下学期必备古诗（共7首）
const poemDatabase = [
    {
        title: "村居",
        author: "高鼎",
        grade: "2",
        content: "草长莺飞二月天，拂堤杨柳醉春烟。儿童散学归来早，忙趁东风放纸鸢。"
    },
    {
        title: "咏柳",
        author: "贺知章",
        grade: "2",
        content: "碧玉妆成一树高，万条垂下绿丝绦。不知细叶谁裁出，二月春风似剪刀。"
    },
    {
        title: "赋得古原草送别",
        author: "白居易",
        grade: "2",
        content: "离离原上草，一岁一枯荣。野火烧不尽，春风吹又生。"
    },
    {
        title: "晓出净慈寺送林子方",
        author: "杨万里",
        grade: "2",
        content: "毕竟西湖六月中，风光不与四时同。接天莲叶无穷碧，映日荷花别样红。"
    },
    {
        title: "绝句",
        author: "杜甫",
        grade: "2",
        content: "两个黄鹂鸣翠柳，一行白鹭上青天。窗含西岭千秋雪，门泊东吴万里船。"
    },
    {
        title: "悯农",
        author: "李绅",
        grade: "2",
        content: "锄禾日当午，汗滴禾下土。谁知盘中餐，粒粒皆辛苦。"
    },
    {
        title: "舟夜书所见",
        author: "查慎行",
        grade: "2",
        content: "月黑见渔灯，孤光一点萤。微微风簇浪，散作满河星。"
    }
];

// 导出数据，使其他JS文件可以使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { poemDatabase };
} else {
    // 在浏览器环境中，将数据挂载到window对象
    window.poemDatabase = poemDatabase;
}
