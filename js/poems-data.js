/**
 * 古诗学习乐园 - 古诗数据
 * 包含所有古诗的基本信息：标题、作者、年级、内容
 */

// 二年级下学期必备古诗（共7首）
const poemDatabase = [
    {
        title: "村居",
        author: "高鼎",
        dynasty: "清代",
        grade: "2",
        content: "草长莺飞二月天，拂堤杨柳醉春烟。儿童散学归来早，忙趁东风放纸鸢。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "莺": "鹰", "醉": "最", "鸢": "鸳", "散": "伞", "趁": "称" },
            similar: { "堤": "提", "杨": "扬", "鸢": "鸳", "童": "瞳", "归": "龟" },
            reverse: ["东风", "纸鸢", "杨柳", "儿童"]
        }
    },
    {
        title: "咏柳",
        author: "贺知章",
        dynasty: "唐代",
        grade: "2",
        content: "碧玉妆成一树高，万条垂下绿丝绦。不知细叶谁裁出，二月春风似剪刀。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "妆": "装", "绦": "涛", "裁": "才", "似": "是", "碧": "壁" },
            similar: { "碧": "壁", "绦": "条", "剪": "箭", "叶": "页", "春": "舂" },
            reverse: ["春风", "剪刀", "细叶", "万条"]
        }
    },
    {
        title: "赋得古原草送别",
        author: "白居易",
        dynasty: "唐代",
        grade: "2",
        content: "离离原上草，一岁一枯荣。野火烧不尽，春风吹又生。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "离": "梨", "岁": "穗", "尽": "进", "荣": "容", "吹": "催" },
            similar: { "原": "园", "荣": "菜", "烧": "浇", "草": "早", "火": "伙" },
            reverse: ["春风", "野火", "原上", "一岁"]
        }
    },
    {
        title: "晓出净慈寺送林子方",
        author: "杨万里",
        dynasty: "宋代",
        grade: "2",
        content: "毕竟西湖六月中，风光不与四时同。接天莲叶无穷碧，映日荷花别样红。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "毕": "必", "竟": "镜", "莲": "连", "映": "应", "别": "别" },
            similar: { "竟": "竞", "碧": "壁", "映": "应", "荷": "何", "样": "洋" },
            reverse: ["西湖", "荷花", "莲叶", "风光"]
        }
    },
    {
        title: "绝句",
        author: "杜甫",
        dynasty: "唐代",
        grade: "2",
        content: "两个黄鹂鸣翠柳，一行白鹭上青天。窗含西岭千秋雪，门泊东吴万里船。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "鹂": "梨", "鹭": "路", "泊": "博", "含": "寒", "岭": "领" },
            similar: { "鹭": "鹜", "岭": "领", "窗": "囱", "泊": "拍", "船": "传" },
            reverse: ["黄鹂", "东吴", "白鹭", "西岭"]
        }
    },
    {
        title: "悯农",
        author: "李绅",
        dynasty: "唐代",
        grade: "2",
        content: "锄禾日当午，汗滴禾下土。四海无闲田，农夫犹饿死。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "锄": "除", "滴": "敌", "闲": "贤", "犹": "由", "饿": "恶" },
            similar: { "禾": "和", "汗": "汉", "田": "甜", "夫": "天", "死": "四" },
            reverse: ["日当", "四海", "农夫", "禾下"]
        }
    },
    {
        title: "舟夜书所见",
        author: "查慎行",
        dynasty: "清代",
        grade: "2",
        content: "月黑见渔灯，孤光一点萤。微微风簇浪，散作满河星。",
        // 古诗小侦探专用错误设置 - 每行都有陷阱
        errors: {
            homophones: { "渔": "鱼", "簇": "促", "作": "坐", "萤": "荧", "散": "伞" },
            similar: { "渔": "鱼", "萤": "荧", "簇": "族", "浪": "狼", "星": "腥" },
            reverse: ["渔灯", "满河", "风簇", "一点"]
        }
    }
];

// 导出数据，使其他JS文件可以使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { poemDatabase };
} else {
    // 在浏览器环境中，将数据挂载到window对象
    window.poemDatabase = poemDatabase;
}
